import React, { useState, useEffect, useRef } from 'react';
import { Card, Input, Button, List, Typography, Select, Space, Alert, Badge, Divider, Tabs } from 'antd';
import { SendOutlined, ClearOutlined, ReloadOutlined, LinkOutlined, DisconnectOutlined, ToolOutlined } from '@ant-design/icons';
import webSocketService from '../services/WebSocketService';
import WebSocketStatus from '../components/WebSocketStatus';
import WebSocketConnectionStatus from '../components/WebSocketConnectionStatus';
import MessageQueueStatus from '../components/MessageQueueStatus';
import WebSocketTestingDashboard from '../components/WebSocketTestingDashboard';

const { TextArea } = Input;
const { Title, Text } = Typography;
const { Option } = Select;

/**
 * WebSocket Test Page
 *
 * A utility page for testing WebSocket connections and sending/receiving messages.
 */
const WebSocketTestPage = () => {
  // State for WebSocket connection
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState(null);

  // State for messages
  const [messages, setMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [messageType, setMessageType] = useState('text');

  // State for custom WebSocket URL
  const [customUrl, setCustomUrl] = useState(process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws/app_builder/');
  const [useCustomUrl, setUseCustomUrl] = useState(false);

  // Ref for message list to auto-scroll
  const messagesEndRef = useRef(null);

  // Connect to WebSocket on component mount
  useEffect(() => {
    // Initialize WebSocket service
    if (!webSocketService.url) {
      webSocketService.init(process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws/app_builder/');
    }

    // Set initial connection state
    setConnected(webSocketService.isConnected);

    // Event handlers
    const handleConnect = () => {
      setConnected(true);
      setConnecting(false);
      setError(null);
      addMessage('system', 'Connected to WebSocket server');
    };

    const handleDisconnect = (event) => {
      setConnected(false);
      setConnecting(false);
      if (event && event.code) {
        setError(`Disconnected (code: ${event.code})`);
        addMessage('system', `Disconnected from WebSocket server (code: ${event.code})`);
      } else {
        setError('Disconnected');
        addMessage('system', 'Disconnected from WebSocket server');
      }
    };

    const handleConnecting = () => {
      setConnecting(true);
      addMessage('system', 'Connecting to WebSocket server...');
    };

    const handleError = (error) => {
      setError(error.message || 'Unknown error');
      addMessage('error', `WebSocket error: ${error.message || 'Unknown error'}`);
    };

    const handleMessage = (message) => {
      addMessage('received', message);
    };

    // Register event listeners
    webSocketService.on('connect', handleConnect);
    webSocketService.on('disconnect', handleDisconnect);
    webSocketService.on('connecting', handleConnecting);
    webSocketService.on('error', handleError);
    webSocketService.on('message', handleMessage);

    // Clean up event listeners on unmount
    return () => {
      webSocketService.off('connect', handleConnect);
      webSocketService.off('disconnect', handleDisconnect);
      webSocketService.off('connecting', handleConnecting);
      webSocketService.off('error', handleError);
      webSocketService.off('message', handleMessage);
    };
  }, []);

  // Auto-scroll to bottom of message list when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add a message to the message list
  const addMessage = (type, content) => {
    const timestamp = new Date().toISOString();
    setMessages(prev => [...prev, { type, content, timestamp }]);
  };

  // State for send options
  const [queueIfOffline, setQueueIfOffline] = useState(true);
  const [persistIfOffline, setPersistIfOffline] = useState(true);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!messageInput.trim()) return;

    let message;

    try {
      // Format message based on type
      switch (messageType) {
        case 'json':
          message = JSON.parse(messageInput);
          break;
        case 'text':
        default:
          message = messageInput;
          break;
      }

      // Send message with options
      const sent = await webSocketService.send(message, {
        queueIfOffline,
        persistIfOffline
      });

      if (sent) {
        addMessage('sent', message);
        setMessageInput('');
      } else if (queueIfOffline) {
        addMessage('queued', message);
        setMessageInput('');
      } else {
        addMessage('error', 'Failed to send message (not connected and queueing disabled)');
      }
    } catch (error) {
      addMessage('error', `Failed to send message: ${error.message}`);
    }
  };

  // Handle clearing messages
  const handleClearMessages = () => {
    setMessages([]);
  };

  // Handle connecting to WebSocket
  const handleConnect = () => {
    if (connected) {
      webSocketService.disconnect();
    } else {
      setConnecting(true);

      if (useCustomUrl && customUrl) {
        webSocketService.init(customUrl);
      } else {
        webSocketService.reconnect();
      }
    }
  };

  // Format message content for display
  const formatMessageContent = (message) => {
    if (typeof message.content === 'object') {
      return JSON.stringify(message.content, null, 2);
    }
    return String(message.content);
  };

  // Get message item style based on message type
  const getMessageItemStyle = (type) => {
    switch (type) {
      case 'sent':
        return { backgroundColor: '#f0f8ff', borderLeft: '4px solid #1890ff' };
      case 'received':
        return { backgroundColor: '#f6ffed', borderLeft: '4px solid #52c41a' };
      case 'queued':
        return { backgroundColor: '#fff7e6', borderLeft: '4px solid #faad14' };
      case 'error':
        return { backgroundColor: '#fff1f0', borderLeft: '4px solid #f5222d' };
      case 'system':
        return { backgroundColor: '#f5f5f5', borderLeft: '4px solid #d9d9d9' };
      default:
        return {};
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>WebSocket Test Page</Title>

      <Tabs defaultActiveKey="connection" style={{ marginBottom: '20px' }}>
        <Tabs.TabPane tab="Connection Settings" key="connection">
          <Card>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                <Text strong style={{ marginRight: '10px' }}>Status:</Text>
                <WebSocketStatus showTooltip={false} />
                <Divider type="vertical" />
                <Text strong style={{ marginRight: '10px', marginLeft: '10px' }}>Enhanced Status:</Text>
                <WebSocketConnectionStatus />
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Input
                  addonBefore="WebSocket URL"
                  value={customUrl}
                  onChange={(e) => setCustomUrl(e.target.value)}
                  disabled={!useCustomUrl}
                  style={{ flex: 1, marginRight: '10px' }}
                />
                <Select
                  value={useCustomUrl}
                  onChange={(value) => setUseCustomUrl(value)}
                  style={{ width: '150px' }}
                >
                  <Option value={false}>Default URL</Option>
                  <Option value={true}>Custom URL</Option>
                </Select>
              </div>

              <Button
                type="primary"
                icon={connected ? <DisconnectOutlined /> : <LinkOutlined />}
                onClick={handleConnect}
                loading={connecting}
                style={{ marginTop: '10px' }}
              >
                {connected ? 'Disconnect' : 'Connect'}
              </Button>

              {error && (
                <Alert
                  message="Connection Error"
                  description={error}
                  type="error"
                  showIcon
                  style={{ marginTop: '10px' }}
                />
              )}
            </Space>
          </Card>
        </Tabs.TabPane>
        <Tabs.TabPane tab="Message Queue" key="queue">
          <MessageQueueStatus showControls={true} showDetails={true} />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={
            <span>
              <ToolOutlined /> Advanced Testing
            </span>
          }
          key="testing"
        >
          <WebSocketTestingDashboard />
        </Tabs.TabPane>
      </Tabs>

      <Card title="Send Message" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', marginBottom: '10px' }}>
            <Select
              value={messageType}
              onChange={(value) => setMessageType(value)}
              style={{ width: '100px', marginRight: '10px' }}
            >
              <Option value="text">Text</Option>
              <Option value="json">JSON</Option>
            </Select>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              disabled={!messageInput.trim()}
              style={{ flex: 1 }}
            >
              Send Message
            </Button>
          </div>

          <TextArea
            rows={4}
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            placeholder={messageType === 'json' ? '{"type": "message", "content": "Hello"}' : 'Enter message to send...'}
          />

          <div style={{ display: 'flex', marginTop: '10px' }}>
            <div style={{ flex: 1 }}>
              <Text strong>Offline Options:</Text>
            </div>
            <Space>
              <div>
                <Text>Queue if offline:</Text>
                <Select
                  value={queueIfOffline}
                  onChange={(value) => setQueueIfOffline(value)}
                  style={{ width: '80px', marginLeft: '8px' }}
                >
                  <Option value={true}>Yes</Option>
                  <Option value={false}>No</Option>
                </Select>
              </div>
              <div>
                <Text>Persist in IndexedDB:</Text>
                <Select
                  value={persistIfOffline}
                  onChange={(value) => setPersistIfOffline(value)}
                  style={{ width: '80px', marginLeft: '8px' }}
                  disabled={!queueIfOffline}
                >
                  <Option value={true}>Yes</Option>
                  <Option value={false}>No</Option>
                </Select>
              </div>
            </Space>
          </div>

          {!connected && queueIfOffline && (
            <Alert
              message="WebSocket is disconnected"
              description="Your message will be queued and sent when the connection is restored."
              type="warning"
              showIcon
              style={{ marginTop: '10px' }}
            />
          )}
        </Space>
      </Card>

      <Card
        title="Messages"
        extra={
          <Button icon={<ClearOutlined />} onClick={handleClearMessages}>
            Clear
          </Button>
        }
      >
        {messages.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Text type="secondary">No messages yet</Text>
          </div>
        ) : (
          <List
            dataSource={messages}
            renderItem={(message) => (
              <List.Item style={{ ...getMessageItemStyle(message.type), padding: '10px' }}>
                <div style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                    <Badge
                      status={
                        message.type === 'sent'
                          ? 'processing'
                          : message.type === 'received'
                            ? 'success'
                            : message.type === 'error'
                              ? 'error'
                              : 'default'
                      }
                      text={message.type.charAt(0).toUpperCase() + message.type.slice(1)}
                    />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </Text>
                  </div>
                  <div>
                    <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {formatMessageContent(message)}
                    </pre>
                  </div>
                </div>
              </List.Item>
            )}
          />
        )}
        <div ref={messagesEndRef} />
      </Card>

      <Divider />

      <div style={{ marginTop: '20px' }}>
        <Title level={4}>WebSocket Testing Tips</Title>
        <ul>
          <li>
            <Text>Use the <Text code>{"{ type: 'ping' }"}</Text> message to test server response</Text>
          </li>
          <li>
            <Text>Check browser console for detailed WebSocket logs</Text>
          </li>
          <li>
            <Text>Test reconnection by temporarily disconnecting your network</Text>
          </li>
          <li>
            <Text>For JSON messages, ensure your JSON is valid before sending</Text>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default WebSocketTestPage;
