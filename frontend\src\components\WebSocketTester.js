import React, { useState, useEffect, useRef } from 'react';
import { Button, Card, Input, Select, Space, Typography, List, Tag, Divider, Switch, Collapse, message } from 'antd';
import { SendOutlined, ReloadOutlined, CloseOutlined, CheckOutlined, WarningOutlined } from '@ant-design/icons';
import { WS_CONFIG, getWebSocketUrl } from '../config/env';
import WebSocketService from '../services/WebSocketService';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

/**
 * WebSocket Tester Component
 *
 * A comprehensive tool for testing WebSocket connections with various endpoints
 * and message types.
 */
const WebSocketTester = () => {
  // WebSocket connection state
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [url, setUrl] = useState(WS_CONFIG.BASE_URL || 'ws://localhost:8000/ws/');
  const [endpoint, setEndpoint] = useState(WS_CONFIG.ENDPOINT || 'app_builder');
  const [secure, setSecure] = useState(window.location.protocol === 'https:');
  const [messages, setMessages] = useState([]);
  const [messageType, setMessageType] = useState('ping');
  const [messageContent, setMessageContent] = useState('');
  const [autoReconnect, setAutoReconnect] = useState(true);
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [pingInterval, setPingInterval] = useState(null);
  const [lastPingTime, setLastPingTime] = useState(null);
  const [pingRoundTrip, setPingRoundTrip] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [connectionStats, setConnectionStats] = useState({
    messagesReceived: 0,
    messagesSent: 0,
    connectionsOpened: 0,
    connectionsClosed: 0,
    errors: 0,
    lastError: null,
  });

  // WebSocket reference
  const ws = useRef(null);
  const reconnectTimeout = useRef(null);
  const messagesEndRef = useRef(null);

  // Predefined endpoints
  const endpoints = [
    { label: 'App Builder', value: 'app_builder' },
    { label: 'App Builder (with ID)', value: 'app/test' },
    { label: 'Test', value: 'test' },
    { label: 'Root', value: '' },
    { label: 'Collaboration', value: 'collaboration' },
    { label: 'Collaboration (with Session)', value: 'collaboration/test' },
    { label: 'Notifications', value: 'notifications' },
    { label: 'Performance Monitor', value: 'performance' },
  ];

  // Message types
  const messageTypes = [
    { label: 'Ping', value: 'ping' },
    { label: 'Echo', value: 'echo' },
    { label: 'Get App Data', value: 'get_app_data' },
    { label: 'Update App Data', value: 'update_app_data' },
    { label: 'Add Component', value: 'add_component' },
    { label: 'Update Component', value: 'update_component' },
    { label: 'Delete Component', value: 'delete_component' },
    { label: 'Collaboration: Edit', value: 'edit' },
    { label: 'Collaboration: Chat', value: 'chat_message' },
    { label: 'Notifications: Mark Read', value: 'mark_as_read' },
    { label: 'Performance: Start Monitoring', value: 'start_monitoring' },
    { label: 'Performance: Stop Monitoring', value: 'stop_monitoring' },
    { label: 'Custom', value: 'custom' },
  ];

  // Sample message templates
  const messageTemplates = {
    ping: { type: 'ping', timestamp: Date.now() },
    echo: { type: 'echo', message: 'Hello WebSocket!', timestamp: Date.now() },
    get_app_data: { type: 'get_app_data', app_id: 'test', timestamp: Date.now() },
    update_app_data: {
      type: 'update_app_data',
      app_id: 'test',
      data: {
        name: 'Test App',
        components: [
          { id: 'comp1', type: 'button', props: { label: 'Click Me' } }
        ]
      },
      timestamp: Date.now()
    },
    add_component: {
      type: 'add_component',
      app_id: 'test',
      component: {
        type: 'button',
        props: { label: 'New Button', variant: 'primary' }
      },
      timestamp: Date.now()
    },
    update_component: {
      type: 'update_component',
      app_id: 'test',
      component_id: 'comp1',
      component: {
        type: 'button',
        props: { label: 'Updated Button', variant: 'secondary' }
      },
      timestamp: Date.now()
    },
    delete_component: {
      type: 'delete_component',
      app_id: 'test',
      component_id: 'comp1',
      timestamp: Date.now()
    },
    edit: {
      type: 'edit',
      session_id: 'test',
      edit_type: 'insert',
      position: { line: 1, column: 0 },
      content: 'Hello, world!',
      timestamp: Date.now()
    },
    chat_message: {
      type: 'chat_message',
      session_id: 'test',
      message: 'Hello, everyone!',
      timestamp: Date.now()
    },
    mark_as_read: {
      type: 'mark_as_read',
      notification_id: '123',
      timestamp: Date.now()
    },
    start_monitoring: {
      type: 'start_monitoring',
      interval: 5,
      timestamp: Date.now()
    },
    stop_monitoring: {
      type: 'stop_monitoring',
      timestamp: Date.now()
    },
    custom: { type: 'custom', timestamp: Date.now() }
  };

  // Connect to WebSocket
  const connect = () => {
    if (connected || connecting) return;

    setConnecting(true);
    setConnectionStatus('connecting');

    try {
      // Build WebSocket URL using the proper backend URL
      const wsUrl = getWebSocketUrl(endpoint, { forceSecure: secure });

      console.log(`Connecting to: ${wsUrl}`);

      // Create WebSocket service instance
      const wsService = WebSocketService.getInstance({
        baseUrl: wsUrl.substring(0, wsUrl.lastIndexOf('/')),
        endpoint: wsUrl.substring(wsUrl.lastIndexOf('/'))
      });

      // Store the WebSocket service in the ref
      ws.current = wsService;

      // Set up event listeners
      wsService.addEventListener('open', handleOpen);
      wsService.addEventListener('message', handleMessage);
      wsService.addEventListener('close', handleClose);
      wsService.addEventListener('error', handleError);

      // Connect to the WebSocket server
      wsService.connect()
        .then(() => {
          console.log('WebSocket connected successfully');
          // Update stats
          setConnectionStats(prev => ({
            ...prev,
            connectionsOpened: prev.connectionsOpened + 1
          }));
        })
        .catch(error => {
          console.error('WebSocket connection failed:', error);
          setConnecting(false);
          setConnectionStatus('error');
          message.error(`Failed to connect: ${error.message}`);

          // Update stats
          setConnectionStats(prev => ({
            ...prev,
            errors: prev.errors + 1,
            lastError: error.message
          }));
        });
    } catch (error) {
      console.error('WebSocket connection error:', error);
      setConnecting(false);
      setConnectionStatus('error');
      message.error(`Failed to connect: ${error.message}`);

      // Update stats
      setConnectionStats(prev => ({
        ...prev,
        errors: prev.errors + 1,
        lastError: error.message
      }));
    }
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    if (!connected && !connecting) return;

    if (ws.current) {
      // Remove event listeners
      ws.current.removeEventListener('open', handleOpen);
      ws.current.removeEventListener('message', handleMessage);
      ws.current.removeEventListener('close', handleClose);
      ws.current.removeEventListener('error', handleError);

      // Close the connection
      ws.current.close(1000, 'User initiated disconnect');
    }

    // Clear ping interval if active
    if (pingInterval) {
      clearInterval(pingInterval);
      setPingInterval(null);
    }

    // Clear reconnect timeout if active
    if (reconnectTimeout.current) {
      clearTimeout(reconnectTimeout.current);
      reconnectTimeout.current = null;
    }

    setConnected(false);
    setConnecting(false);
    setConnectionStatus('disconnected');
    message.info('Disconnected from WebSocket server');
  };

  // Handle WebSocket open event
  const handleOpen = () => {
    setConnected(true);
    setConnecting(false);
    setConnectionStatus('connected');
    message.success('WebSocket connected');

    // Start ping interval
    startPingInterval();
  };

  // Handle WebSocket message event
  const handleMessage = (data) => {
    try {
      // Add received message to list
      setMessages(prev => [...prev, {
        id: Date.now(),
        direction: 'received',
        data,
        timestamp: new Date()
      }]);

      // Handle pong messages for latency calculation
      if (data.type === 'pong' && lastPingTime) {
        const roundTrip = Date.now() - lastPingTime;
        setPingRoundTrip(roundTrip);
        setLastPingTime(null);
      }

      // Update stats
      setConnectionStats(prev => ({
        ...prev,
        messagesReceived: prev.messagesReceived + 1
      }));
    } catch (error) {
      console.error('Error handling message:', error);

      // Add error message to list
      setMessages(prev => [...prev, {
        id: Date.now(),
        direction: 'received',
        data: { type: 'error', message: 'Error handling message', details: error.message },
        error: 'Error handling message',
        timestamp: new Date()
      }]);

      // Update stats
      setConnectionStats(prev => ({
        ...prev,
        errors: prev.errors + 1,
        lastError: 'Error handling message'
      }));
    }
  };

  // Handle WebSocket close event
  const handleClose = (event) => {
    setConnected(false);
    setConnecting(false);
    setConnectionStatus('disconnected');

    // Update stats
    setConnectionStats(prev => ({
      ...prev,
      connectionsClosed: prev.connectionsClosed + 1
    }));

    // Clear ping interval
    if (pingInterval) {
      clearInterval(pingInterval);
      setPingInterval(null);
    }

    // Auto reconnect if enabled
    if (autoReconnect && !event.wasClean) {
      message.info('Connection closed. Reconnecting...');
      setConnectionStatus('reconnecting');

      // Reconnect after delay
      reconnectTimeout.current = setTimeout(() => {
        connect();
      }, 3000);
    } else {
      if (event.wasClean) {
        message.info(`Connection closed cleanly, code=${event.code}`);
      } else {
        message.error(`Connection died, code=${event.code}`);
      }
    }
  };

  // Handle WebSocket error event
  const handleError = (error) => {
    console.error('WebSocket error:', error);
    setConnectionStatus('error');

    // Update stats
    setConnectionStats(prev => ({
      ...prev,
      errors: prev.errors + 1,
      lastError: 'WebSocket error'
    }));

    message.error('WebSocket error occurred');
  };

  // Send a message to the WebSocket
  const sendMessage = () => {
    if (!connected || !ws.current) {
      message.error('Not connected to WebSocket');
      return;
    }

    try {
      // Get message template based on type
      let messageData = { ...messageTemplates[messageType] };

      // Update timestamp
      messageData.timestamp = Date.now();

      // If custom message content is provided, try to parse it
      if (messageType === 'custom' && messageContent) {
        try {
          messageData = JSON.parse(messageContent);
        } catch (error) {
          message.error('Invalid JSON in custom message');
          return;
        }
      } else if (messageContent && messageType === 'echo') {
        // For echo messages, update the message field
        messageData.message = messageContent;
      }

      // If it's a ping message, store the time for latency calculation
      if (messageType === 'ping') {
        setLastPingTime(Date.now());
      }

      // Send the message using the WebSocketService
      ws.current.sendMessage(messageData)
        .then(() => {
          console.log('Message sent successfully');

          // Add sent message to list
          setMessages(prev => [...prev, {
            id: Date.now(),
            direction: 'sent',
            data: messageData,
            timestamp: new Date()
          }]);

          // Update stats
          setConnectionStats(prev => ({
            ...prev,
            messagesSent: prev.messagesSent + 1
          }));

          // Clear message content for custom messages
          if (messageType === 'custom') {
            setMessageContent('');
          }
        })
        .catch(error => {
          console.error('Error sending message:', error);
          message.error(`Failed to send message: ${error.message}`);

          // Update stats
          setConnectionStats(prev => ({
            ...prev,
            errors: prev.errors + 1,
            lastError: `Failed to send message: ${error.message}`
          }));
        });
    } catch (error) {
      console.error('Error preparing message:', error);
      message.error(`Failed to prepare message: ${error.message}`);

      // Update stats
      setConnectionStats(prev => ({
        ...prev,
        errors: prev.errors + 1,
        lastError: `Failed to prepare message: ${error.message}`
      }));
    }
  };

  // Start ping interval
  const startPingInterval = () => {
    // Clear existing interval if any
    if (pingInterval) {
      clearInterval(pingInterval);
    }

    // Set new interval
    const interval = setInterval(() => {
      if (connected && ws.current) {
        // Send ping message
        const pingMessage = {
          type: 'ping',
          timestamp: Date.now()
        };

        try {
          // Send ping using WebSocketService
          ws.current.sendMessage(pingMessage)
            .then(() => {
              setLastPingTime(Date.now());

              // Update stats
              setConnectionStats(prev => ({
                ...prev,
                messagesSent: prev.messagesSent + 1
              }));
            })
            .catch(error => {
              console.error('Error sending ping:', error);

              // Update stats
              setConnectionStats(prev => ({
                ...prev,
                errors: prev.errors + 1,
                lastError: 'Error sending ping: ' + error.message
              }));

              // Clear interval on error
              clearInterval(interval);
              setPingInterval(null);
            });
        } catch (error) {
          console.error('Error preparing ping:', error);

          // Update stats
          setConnectionStats(prev => ({
            ...prev,
            errors: prev.errors + 1,
            lastError: 'Error preparing ping: ' + error.message
          }));

          // Clear interval on error
          clearInterval(interval);
          setPingInterval(null);
        }
      } else {
        // Clear interval if not connected
        clearInterval(interval);
        setPingInterval(null);
      }
    }, 30000); // 30 seconds

    setPingInterval(interval);
  };

  // Clear messages
  const clearMessages = () => {
    setMessages([]);
  };

  // Format message for display
  const formatMessage = (message) => {
    try {
      return JSON.stringify(message, null, 2);
    } catch (error) {
      return String(message);
    }
  };

  // Get message tag color based on type
  const getMessageTagColor = (message) => {
    if (message.error) return 'error';
    if (message.direction === 'sent') return 'blue';

    // For received messages, color based on message type
    if (message.data && message.data.type) {
      switch (message.data.type) {
        case 'error':
          return 'error';
        case 'pong':
          return 'green';
        case 'connection_established':
          return 'success';
        case 'app_data':
        case 'component_added':
        case 'component_updated':
          return 'processing';
        default:
          return 'purple';
      }
    }

    return 'default';
  };

  // Get connection status tag
  const getConnectionStatusTag = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Tag color="success" icon={<CheckOutlined />}>Connected</Tag>;
      case 'connecting':
        return <Tag color="processing">Connecting...</Tag>;
      case 'reconnecting':
        return <Tag color="warning">Reconnecting...</Tag>;
      case 'error':
        return <Tag color="error" icon={<WarningOutlined />}>Error</Tag>;
      case 'disconnected':
      default:
        return <Tag color="default" icon={<CloseOutlined />}>Disconnected</Tag>;
    }
  };

  // Scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Disconnect WebSocket
      if (ws.current) {
        ws.current.close();
      }

      // Clear intervals and timeouts
      if (pingInterval) {
        clearInterval(pingInterval);
      }

      if (reconnectTimeout.current) {
        clearTimeout(reconnectTimeout.current);
      }
    };
  }, []);

  // Update message template when type changes
  useEffect(() => {
    if (messageType === 'custom') {
      setMessageContent(JSON.stringify(messageTemplates.custom, null, 2));
    } else {
      setMessageContent('');
    }
  }, [messageType]);

  return (
    <div className="websocket-tester">
      <Card title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4}>WebSocket Tester</Title>
          {getConnectionStatusTag()}
        </div>
      }>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* Connection Controls */}
          <Card size="small" title="Connection">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', gap: '8px' }}>
                <Select
                  style={{ width: '200px' }}
                  value={endpoint}
                  onChange={setEndpoint}
                  disabled={connected || connecting}
                >
                  {endpoints.map(endpoint => (
                    <Option key={endpoint.value} value={endpoint.value}>
                      {endpoint.label}
                    </Option>
                  ))}
                </Select>
                <Switch
                  checkedChildren="WSS"
                  unCheckedChildren="WS"
                  checked={secure}
                  onChange={setSecure}
                  disabled={connected || connecting}
                />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Space>
                  <Button
                    type={connected ? 'default' : 'primary'}
                    onClick={connected ? disconnect : connect}
                    loading={connecting}
                    danger={connected}
                    icon={connected ? <CloseOutlined /> : <CheckOutlined />}
                  >
                    {connected ? 'Disconnect' : connecting ? 'Connecting...' : 'Connect'}
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => {
                      disconnect();
                      setTimeout(connect, 500);
                    }}
                    disabled={connecting}
                  >
                    Reconnect
                  </Button>
                </Space>
                <Space>
                  <Text>Auto Reconnect:</Text>
                  <Switch
                    checked={autoReconnect}
                    onChange={setAutoReconnect}
                  />
                </Space>
              </div>
            </Space>
          </Card>

          {/* Connection Stats */}
          <Collapse>
            <Panel header="Connection Statistics" key="stats">
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                <div>
                  <Text strong>Messages Sent:</Text> {connectionStats.messagesSent}
                </div>
                <div>
                  <Text strong>Messages Received:</Text> {connectionStats.messagesReceived}
                </div>
                <div>
                  <Text strong>Connections Opened:</Text> {connectionStats.connectionsOpened}
                </div>
                <div>
                  <Text strong>Connections Closed:</Text> {connectionStats.connectionsClosed}
                </div>
                <div>
                  <Text strong>Errors:</Text> {connectionStats.errors}
                </div>
                <div>
                  <Text strong>Last Ping RTT:</Text> {pingRoundTrip !== null ? `${pingRoundTrip}ms` : 'N/A'}
                </div>
              </div>
              {connectionStats.lastError && (
                <div style={{ marginTop: '8px' }}>
                  <Text type="danger" strong>Last Error:</Text> {connectionStats.lastError}
                </div>
              )}
            </Panel>
          </Collapse>

          {/* Message Sender */}
          <Card size="small" title="Send Message">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', gap: '8px' }}>
                <Select
                  style={{ width: '200px' }}
                  value={messageType}
                  onChange={setMessageType}
                  disabled={!connected}
                >
                  {messageTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={sendMessage}
                  disabled={!connected}
                >
                  Send
                </Button>
              </div>

              {messageType === 'custom' && (
                <Input.TextArea
                  rows={6}
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                  placeholder="Enter JSON message"
                  disabled={!connected}
                />
              )}

              {messageType === 'echo' && (
                <Input
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                  placeholder="Enter message content"
                  disabled={!connected}
                />
              )}

              {messageType !== 'custom' && messageType !== 'echo' && (
                <Paragraph type="secondary" style={{ marginTop: '8px' }}>
                  Will send a pre-configured {messageType} message.
                </Paragraph>
              )}
            </Space>
          </Card>

          {/* Messages Display */}
          <Card
            size="small"
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>Messages</span>
                <Space>
                  <Text>Show Timestamps:</Text>
                  <Switch
                    checked={showTimestamps}
                    onChange={setShowTimestamps}
                    size="small"
                  />
                  <Button size="small" onClick={clearMessages}>Clear</Button>
                </Space>
              </div>
            }
            style={{ maxHeight: '400px', overflow: 'auto' }}
          >
            <List
              dataSource={messages}
              renderItem={(message) => (
                <List.Item>
                  <div style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                      <Tag color={getMessageTagColor(message)}>
                        {message.direction === 'sent' ? 'Sent' : 'Received'}
                        {message.data && message.data.type && `: ${message.data.type}`}
                      </Tag>
                      {showTimestamps && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {message.timestamp.toLocaleTimeString()}
                        </Text>
                      )}
                    </div>
                    <pre style={{
                      margin: 0,
                      padding: '8px',
                      background: '#f5f5f5',
                      borderRadius: '4px',
                      overflow: 'auto',
                      maxHeight: '200px'
                    }}>
                      {formatMessage(message.data)}
                    </pre>
                    {message.error && (
                      <Text type="danger">{message.error}</Text>
                    )}
                  </div>
                </List.Item>
              )}
              locale={{ emptyText: 'No messages yet' }}
            />
            <div ref={messagesEndRef} />
          </Card>
        </Space>
      </Card>
    </div>
  );
};

export default WebSocketTester;
