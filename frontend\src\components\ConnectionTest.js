import React, { useState, useEffect } from 'react';

const ConnectionTest = () => {
  const [apiStatus, setApiStatus] = useState('Testing...');
  const [wsStatus, setWsStatus] = useState('Testing...');

  useEffect(() => {
    // Test API connection
    fetch('/api/health/')
      .then(response => {
        if (response.ok) {
          return response.json();
        }
        throw new Error(`HTTP error ${response.status}`);
      })
      .then(data => {
        setApiStatus(`Connected (${data.status || 'OK'})`);
      })
      .catch(error => {
        setApiStatus(`Failed: ${error.message}`);
      });

    // Test WebSocket connection
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = 'localhost:8000';
      const wsUrl = `${protocol}//${host}/ws/app_builder/`;

      console.log('Connecting to WebSocket:', wsUrl);
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        setWsStatus('Connected');
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setWsStatus('Error connecting');
      };

      ws.onclose = (event) => {
        if (event.wasClean) {
          setWsStatus(`Closed: ${event.code}`);
        } else {
          setWsStatus('Connection failed');
        }
      };

      // Clean up on unmount
      return () => {
        ws.close();
      };
    } catch (error) {
      console.error('WebSocket setup error:', error);
      setWsStatus(`Setup error: ${error.message}`);
    }
  }, []);

  return (
    <div className="connection-test">
      <h3>Connection Test</h3>
      <div className="test-results">
        <div className="test-item">
          <strong>API:</strong> <span className={apiStatus.includes('Connected') ? 'success' : 'error'}>{apiStatus}</span>
        </div>
        <div className="test-item">
          <strong>WebSocket:</strong> <span className={wsStatus.includes('Connected') ? 'success' : 'error'}>{wsStatus}</span>
        </div>
      </div>
      <style jsx>{`
        .connection-test {
          margin: 20px;
          padding: 15px;
          border: 1px solid #ddd;
          border-radius: 5px;
        }
        .test-results {
          margin-top: 10px;
        }
        .test-item {
          margin: 5px 0;
        }
        .success {
          color: green;
        }
        .error {
          color: red;
        }
      `}</style>
    </div>
  );
};

export default ConnectionTest;